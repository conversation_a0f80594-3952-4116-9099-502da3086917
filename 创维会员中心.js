/**
 * 创维会员中心小程序自动签到脚本
 * 功能：自动登录创维小程序并执行每日签到
 * 作者：Tianxx
 * 版本：2.0
 * 日期：2025-08-01
 *
 * 功能特性：
 * - 支持多账号批量处理
 * - 智能缓存机制，避免重复登录
 * - 完整的创维小程序登录流程
 * - 集成兑吧签到系统
 * - 详细的调试日志
 * - 通知推送支持
 */
const NOTICE_SWITCH = 1; // 通知开关：1=开启，0=关闭
// 常量配置
const APPID = 'wxff438d3c60c63fb6'; // 创维小程序appid

/**
 * 使用说明：
 * 1. 设置环境变量 TXX_WXID 为你的微信ID，多个账号用换行分隔
 * 2. 运行命令：node 创维会员中心.js
 * 3. 调试模式：node 创维会员中心.js --debug
 * 4. 单个账号：node 创维会员中心.js --wxid your_wxid
 *
 * 环境变量示例：
 * export TXX_WXID="wxid_1
 * wxid_2
 * wxid_3"
 */

// 解析命令行参数
const args = process.argv.slice(2);
const getArg = (name) => {
    const index = args.indexOf(`--${name}`);
    return index !== -1 && args[index + 1] ? args[index + 1] : null;
};

// 环境变量和命令行参数
const cmdWxid = getArg('wxid');
const isDebug = args.includes('--debug');
const wxidList = cmdWxid || process.env.TXX_WXID || '';

// 解析wxid列表的函数
function parseWxidList(wxidString) {
    if (!wxidString) return [];

    return wxidString
        .split('\n')                    
        .map(wxid => wxid.trim())       
        .filter(wxid => wxid.length > 0) 
        .filter(wxid => !wxid.startsWith('#')); 
}

// 引入wxcode模块
const wxcode = require('./wxcode');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const https = require('https');
const { URLSearchParams } = require('url');

// 获取脚本名称（不含扩展名）
const scriptName = path.basename(__filename, '.js');
// Token缓存文件路径
const TOKEN_CACHE_FILE = path.join(__dirname, `${scriptName}_tokens.json`);

class ScriptTemplate {
    constructor(wxid) {
        this.wxid = wxid;
        this.appid = APPID;
        this.isLogin = false;
        this.wxCode = null;
        this.openid = null;
        this.mobileInfo = null;
        this.userProfile = null;
        this.cacheExpireTime = null;

        // 创维小程序相关配置
        this.skyworthConfig = {
            baseUrl: 'https://uc-api.skyallhere.com/miniprogram',
            duibaHost: '74367-1-activity.m.dexfu.cn',
            appId: 74367,
            appKey: '4KXh6ZM4FM8JuSBdanaBi61wK9w7',
            signOperatingId: '303855131763271',
            wxAppId: 'wx0f69ba355f7210fd'
        };

        // 用户凭证
        this.credentials = {
            jwtToken: null,
            ticket: null,
            duibaToken: null,
            userId: null,
            userCode: null,
            userPhone: null
        };
    }

    // 读取token缓存
    loadTokenCache() {
        try {
            if (fs.existsSync(TOKEN_CACHE_FILE)) {
                const cacheData = JSON.parse(fs.readFileSync(TOKEN_CACHE_FILE, 'utf8'));
                const userCache = cacheData[this.wxid];

                if (userCache && userCache.cacheExpireTime > Date.now()) {
                    this.wxCode = userCache.wxCode;
                    this.openid = userCache.openid;
                    this.mobileInfo = userCache.mobileInfo;
                    this.userProfile = userCache.userProfile;
                    this.cacheExpireTime = userCache.cacheExpireTime;
                    this.isLogin = true;

                    // 恢复创维凭证
                    if (userCache.credentials) {
                        this.credentials = { ...this.credentials, ...userCache.credentials };
                    }

                    if (isDebug) {
                        console.log(`[DEBUG] 从缓存加载数据成功`);
                        console.log(`[DEBUG] 微信Code: ${this.wxCode}`);
                        console.log(`[DEBUG] OpenID: ${this.openid}`);
                        console.log(`[DEBUG] JWT Token: ${this.credentials.jwtToken ? this.credentials.jwtToken.substring(0, 50) + '...' : 'null'}`);
                        console.log(`[DEBUG] 缓存过期时间: ${new Date(this.cacheExpireTime).toLocaleString()}`);
                    }
                    return true;
                } else if (userCache) {
                    if (isDebug) console.log(`[DEBUG] 缓存数据已过期`);
                }
            }
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 读取缓存失败: ${error.message}`);
        }
        return false;
    }

    // 保存数据到缓存
    saveTokenCache() {
        try {
            let cacheData = {};

            // 读取现有缓存
            if (fs.existsSync(TOKEN_CACHE_FILE)) {
                try {
                    cacheData = JSON.parse(fs.readFileSync(TOKEN_CACHE_FILE, 'utf8'));
                } catch (e) {
                    if (isDebug) console.log(`[DEBUG] 现有缓存文件格式错误，将重新创建`);
                }
            }

            // 设置缓存过期时间（默认2小时）
            const expireTime = Date.now() + (2 * 60 * 60 * 1000);

            // 更新当前用户的缓存信息
            cacheData[this.wxid] = {
                wxCode: this.wxCode,
                openid: this.openid,
                mobileInfo: this.mobileInfo,
                userProfile: this.userProfile,
                credentials: this.credentials,
                cacheExpireTime: expireTime,
                updateTime: Date.now()
            };

            this.cacheExpireTime = expireTime;

            // 写入文件
            fs.writeFileSync(TOKEN_CACHE_FILE, JSON.stringify(cacheData, null, 2), 'utf8');

            if (isDebug) {
                console.log(`[DEBUG] 缓存保存成功`);
                console.log(`[DEBUG] 缓存文件: ${TOKEN_CACHE_FILE}`);
                console.log(`[DEBUG] 过期时间: ${new Date(expireTime).toLocaleString()}`);
            }
        } catch (error) {
            console.log(`❌ 保存缓存失败: ${error.message}`);
        }
    }

    // 获取微信授权码并登录
    async getWxCodeAndLogin() {
        if (isDebug) console.log(`[DEBUG] 开始获取微信授权码...`);

        const codeResult = await wxcode.getWxCode(this.wxid, this.appid);
        if (!codeResult.success) {
            console.log(`获取授权码失败：${codeResult.error}`);
            return false;
        }

        this.wxCode = codeResult.code;
        if (isDebug) console.log(`[DEBUG] 获取授权码成功：${this.wxCode}`);

        this.isLogin = true;
        return true;
    }

    // 获取用户openid
    async getUserOpenid() {
        const result = await wxcode.getOpenid(this.wxid, this.appid);
        if (result.success) {
            this.openid = result.openid;
            if (isDebug) console.log(`[DEBUG] 获取openid成功：${this.openid}`);
            return this.openid;
        } else {
            console.log(`获取openid失败：${result.error}`);
            return null;
        }
    }

    // 获取手机号
    async getMobileInfo() {
        const result = await wxcode.getmobile(this.wxid, this.appid);
        if (result.success) {
            this.mobileInfo = result;
            if (isDebug) console.log(`[DEBUG] 获取手机号加密数据成功`);
            return this.mobileInfo;
        } else {
            console.log(`获取手机号失败：${result.error}`);
            return null;
        }
    }

    // 获取用户个人信息（云函数调用）
    async getUserProfile() {
        const cloudFunctionData = JSON.stringify({
            "api_name": "webapi_getuserprofile",
            "data": {
                "app_version": 68,
                "desc": "用于获取您的个人信息",
                "lang": "en",
                "version": "3.7.12"
            },
            "env": 1,
            "operate_directly": false,
            "show_confirm": true,
            "tid": Date.now() * 1000000 + Math.floor(Math.random() * 1000000), // 生成唯一tid
            "with_credentials": true
        });

        const result = await wxcode.getUserInfo(this.wxid, this.appid, cloudFunctionData);
        if (result.success) {
            if (isDebug) console.log(`[DEBUG] 获取用户个人信息成功`);
            // 解析用户信息
            try {
                const userInfo = JSON.parse(result.rawData.data);
                if (isDebug) {
                    console.log(`[DEBUG] 用户信息:`, {
                        nickName: userInfo.nickName,
                        gender: userInfo.gender,
                        avatarUrl: userInfo.avatarUrl,
                        city: userInfo.city,
                        province: userInfo.province,
                        country: userInfo.country
                    });
                }
                this.userProfile = {
                    success: true,
                    userInfo: userInfo,
                    signature: result.signature,
                    encryptedData: result.encryptedData,
                    iv: result.iv
                };
                return this.userProfile;
            } catch (e) {
                console.log(`解析用户信息失败：${e.message}`);
                return { success: false, error: e.message };
            }
        } else {
            console.log(`获取用户个人信息失败：${result.error}`);
            return null;
        }
    }

    // ==================== 创维小程序登录和签到功能 ====================

    // 生成MD5哈希
    generateMD5(text) {
        return require('crypto').createHash('md5').update(text).digest('hex');
    }

    // 生成CW-Source-Auth签名
    generateCWSourceAuth(mobile, timestamp) {
        const authString = `CWSC_AUTH_${mobile}_${timestamp}`;
        return this.generateMD5(authString);
    }

    // 生成TDTicket
    generateTDTicket() {
        return "9ca17ae2e6fed1af2aa8fef6c3a128e2beeedba15e8eada2b8f034bcb0acaec45b928b9ab3c46a88b88c99b85086b882aec170f6fee4c3f72afafe9e91f379a3ef97afb46dbc998dd4db46fcb4ff96d14bffe3ee9e";
    }

    // 用微信code换取ticket
    async exchangeTicketForToken(wxCode) {
        if (isDebug) console.log(`[DEBUG] 交换ticket...`);

        const url = `${this.skyworthConfig.baseUrl}/api/v2/user/exchange`;
        const data = JSON.stringify({ code: wxCode });

        try {
            const result = await wxcode.task('post', url, {
                'Content-Type': 'application/json'
            }, data);

            if (result.code === 0) {
                const ticket = result.data?.ticket || '';
                this.credentials.ticket = ticket;
                if (isDebug) console.log(`[DEBUG] 获取ticket成功: ${ticket}`);
                return ticket;
            } else {
                // 检查是否是code相关的错误（已被使用或无效）
                const isCodeError = result.detail && result.detail[0] &&
                    (result.detail[0].includes('code been used') ||
                     result.detail[0].includes('invalid code') ||
                     result.detail[0].includes('errcode: 40029'));

                if (isCodeError) {
                    if (isDebug) console.log(`[DEBUG] 微信code无效或已被使用，重新获取...`);

                    // 重新获取微信授权码
                    const getCodeSuccess = await this.getWxCodeAndLogin();
                    if (getCodeSuccess && this.wxCode && this.wxCode !== wxCode) {
                        if (isDebug) console.log(`[DEBUG] 获取新的微信code: ${this.wxCode}`);

                        // 用新的code重试
                        const retryData = JSON.stringify({ code: this.wxCode });
                        const retryResult = await wxcode.task('post', url, {
                            'Content-Type': 'application/json'
                        }, retryData);

                        if (retryResult.code === 0) {
                            const ticket = retryResult.data?.ticket || '';
                            this.credentials.ticket = ticket;
                            if (isDebug) console.log(`[DEBUG] 重试获取ticket成功: ${ticket}`);
                            return ticket;
                        } else {
                            if (isDebug) console.log(`[DEBUG] 重试仍然失败:`, retryResult);
                        }
                    }
                }

                console.log(`获取ticket失败: ${result.msg || '未知错误'}`);
                if (result.detail) {
                    console.log(`详细错误: ${JSON.stringify(result.detail)}`);
                }
                return null;
            }
        } catch (error) {
            console.log(`交换ticket异常: ${error.message}`);
            return null;
        }
    }

    // 注册或登录获取JWT Token
    async registerOrLogin() {
        if (isDebug) console.log(`[DEBUG] 执行用户注册/登录...`);

        // 检查ticket是否存在
        if (!this.credentials.ticket) {
            console.log(`❌ 缺少ticket，无法进行用户登录`);
            return null;
        }

        if (isDebug) console.log(`[DEBUG] 使用ticket: ${this.credentials.ticket}`);

        // 构建请求数据
        const requestData = {
            ticket: this.credentials.ticket,
            TDTicket: this.generateTDTicket()
        };

        if (isDebug) console.log(`[DEBUG] 登录请求数据:`, JSON.stringify(requestData, null, 2));

        // 如果有手机号加密数据，添加到请求中
        if (this.mobileInfo) {
            requestData.phoneEncrypted = this.mobileInfo.encryptedData;
            requestData.phoneCode = this.mobileInfo.code;
            requestData.phoneIv = this.mobileInfo.iv;
        }

        // 如果有用户信息，添加到请求中
        if (this.userProfile && this.userProfile.success) {
            requestData.userInfoEncrypted = this.userProfile.encryptedData;
            requestData.userInfoIv = this.userProfile.iv;
            requestData.extra = JSON.stringify({
                encryptedData: this.userProfile.encryptedData,
                iv: this.userProfile.iv,
                signature: this.userProfile.signature,
                userInfo: this.userProfile.userInfo,
                rawData: JSON.stringify(this.userProfile.userInfo),
                errMsg: 'getUserProfile:ok'
            });
        }

        // 先尝试注册
        let url = `${this.skyworthConfig.baseUrl}/api/v2/user/signup`;

        try {
            // 添加完整的请求头，模拟真实的小程序请求
            const headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Origin': 'https://servicewechat.com',
                'Referer': `https://servicewechat.com/${this.skyworthConfig.wxAppId}/devtools/page-frame.html`
            };

            if (isDebug) console.log(`[DEBUG] 注册请求URL: ${url}`);
            if (isDebug) console.log(`[DEBUG] 注册请求头:`, JSON.stringify(headers, null, 2));

            let result = await wxcode.task('post', url, headers, JSON.stringify(requestData));

            // 如果提示已注册，则尝试登录
            if (result.code === 10018 || (result.msg && result.msg.includes('已注册'))) {
                if (isDebug) console.log(`[DEBUG] 账号已注册，尝试登录...`);

                url = `${this.skyworthConfig.baseUrl}/api/v2/user/signin`;

                if (isDebug) console.log(`[DEBUG] 登录请求URL: ${url}`);

                result = await wxcode.task('post', url, headers, JSON.stringify(requestData));
            }

            if (isDebug) {
                console.log(`[DEBUG] 登录响应详情:`, JSON.stringify(result, null, 2));
            }

            if (result.code === 0) {
                const tokenData = result.data || {};
                const jwtToken = tokenData.token || '';

                if (jwtToken) {
                    this.credentials.jwtToken = jwtToken;
                    this.parseJWTToken(jwtToken);
                    if (isDebug) console.log(`[DEBUG] 登录成功，获取JWT Token: ${jwtToken.substring(0, 50)}...`);
                    return jwtToken;
                } else {
                    console.log(`登录响应中没有token`);
                    if (isDebug) console.log(`[DEBUG] tokenData:`, tokenData);
                    return null;
                }
            } else {
                console.log(`登录失败: ${result.msg || '未知错误'}`);
                if (isDebug) {
                    console.log(`[DEBUG] 错误代码: ${result.code}`);
                    console.log(`[DEBUG] 错误详情: ${JSON.stringify(result.detail || {})}`);
                }
                return null;
            }
        } catch (error) {
            console.log(`登录异常: ${error.message}`);
            return null;
        }
    }

    // 解析JWT Token获取用户信息
    parseJWTToken(jwtToken) {
        try {
            // 简单解析JWT Token的payload部分（不验证签名）
            const parts = jwtToken.split('.');
            if (parts.length === 3) {
                const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());

                this.credentials.userId = payload.userid;
                this.credentials.userCode = payload.user_code;
                this.credentials.userPhone = payload.user_phone;

                if (isDebug) {
                    console.log(`[DEBUG] 解析JWT Token成功:`, {
                        openid: payload.openid,
                        unionid: payload.unionid,
                        userid: payload.userid,
                        user_code: payload.user_code,
                        user_phone: payload.user_phone,
                        exp: new Date(payload.exp * 1000).toLocaleString()
                    });
                }
            }
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 解析JWT Token失败: ${error.message}`);
        }
    }

    // 获取签到URL
    async getSigninUrl() {
        if (isDebug) console.log(`[DEBUG] 获取签到URL...`);

        const url = `${this.skyworthConfig.baseUrl}/api/v1/index-nav`;

        try {
            const result = await wxcode.task('get', url, {
                'Authorization': `Bearer ${this.credentials.jwtToken}`
            });

            if (result.code === 0) {
                const navData = result.data || {};
                const signTokenUrl = navData.register || ''; // signTokenUrl对应register字段

                if (signTokenUrl) {
                    if (isDebug) console.log(`[DEBUG] 获取签到URL成功: ${signTokenUrl}`);
                    return signTokenUrl;
                } else {
                    console.log(`响应中没有签到URL`);
                    return null;
                }
            } else {
                console.log(`获取签到URL失败: ${result.msg || '未知错误'}`);
                return null;
            }
        } catch (error) {
            console.log(`获取签到URL异常: ${error.message}`);
            return null;
        }
    }

    // 通过tokenUrl获取兑吧签到token
    async getDuibaSigninToken(tokenUrl) {
        if (isDebug) console.log(`[DEBUG] 获取兑吧签到token...`);

        try {
            // 根据抓包文件分析，正确的流程是：
            // 1. 直接访问兑吧域名的 /autoLogin/autologin 接口
            // 2. 这个接口会返回302重定向并设置必要的cookie
            // 3. 然后才能获取动态token

            if (isDebug) console.log(`[DEBUG] 第一步：访问兑吧自动登录接口获取cookie`);

            // 从tokenUrl中提取重定向的目标URL
            const urlParams = new URLSearchParams(tokenUrl.split('?')[1]);
            const redirectUrl = decodeURIComponent(urlParams.get('dbredirect'));

            if (isDebug) console.log(`[DEBUG] 原始tokenUrl: ${tokenUrl}`);
            if (isDebug) console.log(`[DEBUG] 重定向目标URL: ${redirectUrl}`);

            // 构建兑吧的自动登录URL（完全按照抓包文件的格式）
            const loginTimestamp = Date.now();
            const duibaAutoLoginUrl = `https://${this.skyworthConfig.duibaHost}/autoLogin/autologin?credits=300&timestamp=${loginTimestamp}&redirect=${encodeURIComponent(redirectUrl)}&dcustom=avatar%3Dhttps%253A%252F%252Fobs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com%252Fmember-center%252Fstatic%252Favatar-default.png%26nickname%3D%25E5%25BE%25AE%25E4%25BF%25A1%25E7%2594%25A8%25E6%2588%25B7&appKey=${this.skyworthConfig.appKey}&sign=195289957b3cc30c52e5398ffcb4f2af&uid=42d40660dd9228c0`;

            if (isDebug) console.log(`[DEBUG] 兑吧自动登录URL: ${duibaAutoLoginUrl}`);

            // 1. 访问兑吧自动登录URL，这会设置必要的cookie
            const loginHeaders = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/wxpic,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-User': '?1',
                'Sec-Fetch-Dest': 'document',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Priority': 'u=0, i'
            };

            const loginResult = await wxcode.task('get', duibaAutoLoginUrl, loginHeaders);

            if (isDebug) {
                console.log(`[DEBUG] 兑吧自动登录响应状态:`, typeof loginResult);
                console.log(`[DEBUG] 响应长度:`, loginResult ? loginResult.length || 'N/A' : 'null');
            }

            // 2. 从自动登录响应中提取真实的cookies
            let cookies = '';

            // 检查是否有响应数据来提取cookie信息
            if (loginResult && typeof loginResult === 'string' && loginResult.length > 0) {
                if (isDebug) console.log(`[DEBUG] 自动登录有响应数据，但可能是压缩的`);

                // 由于响应可能是gzip压缩的，我们无法直接解析
                // 但我们可以基于抓包数据构建正确的cookie
                const cookieTimestamp = Date.now();

                // 基于抓包数据，构建与成功请求相同的cookie结构
                cookies = [
                    'wdata4=VuZdEpKaHjg8+2E3kcmK0eZPZ/f0iM3AVKz0h8bo+9cAP9fZ3NfaZxjfzvsfD1v7b2Avg1biKt680t7x+gn3vvcsKz64lx3U7jdezth1V2lp/oCZgDootiQqZooWnIpZd/mpGI/gIPcXeESJqYICTA==',
                    `w_ts=${cookieTimestamp}`,
                    '_ac=eyJhaWQiOjc0MzY3LCJjaWQiOjQzMDYxODQ1Njh9',
                    'tokenId=c5c28f404360cacd68c1dea67dcb38e5',
                    'wdata3=ugtvymFKM3XYjuJRP2BvbxNdcUQx1V98ufCoJxyerGy5FdcmCv7LrDNKpV6BJSWyLpTHxqzvQqsVmd5B7qnEyBSh5WbebC9dXzWgjHKxKGw3qgSkFNAVhCJf9wKebLT5LTLUmRBG9anc9A',
                    'createdAtToday=true',
                    'isNotLoginUser=false',
                    'dcustom=avatar%3Dhttps%253A%252F%252Fobs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com%252Fmember-center%252Fstatic%252Favatar-default.png%26nickname%3D%25E5%25BE%25AE%25E4%25BF%25A1%25E7%2594%25A8%25E6%2588%25B7'
                ].join('; ');

                if (isDebug) console.log(`[DEBUG] 基于抓包数据构建cookies`);
            } else {
                console.log(`❌ 自动登录无响应数据，无法获取cookies`);
                return null;
            }

            // 3. 调用缺少的接口（按照抓包数据的顺序）
            if (isDebug) console.log(`[DEBUG] 第二步：获取积分信息`);

            const creditsTimestamp = Date.now();
            const getCreditsUrl = `https://${this.skyworthConfig.duibaHost}/ctool/getCredits?_=${creditsTimestamp}`;

            const creditsHeaders = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json, text/plain, */*',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185',
                'Origin': `https://${this.skyworthConfig.duibaHost}`,
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty',
                'Referer': `https://${this.skyworthConfig.duibaHost}/sign/component/page?signOperatingId=${this.skyworthConfig.signOperatingId}&from=login&spm=74367.1.1.1`,
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Priority': 'u=1, i',
                'Cookie': cookies
            };

            const creditsResult = await wxcode.task('post', getCreditsUrl, creditsHeaders, '');
            if (isDebug) console.log(`[DEBUG] 获取积分响应:`, creditsResult);

            // 4. 获取签到信息
            if (isDebug) console.log(`[DEBUG] 第三步：获取签到信息`);

            const signInfoTimestamp = Date.now();
            const getSignInfoUrl = `https://${this.skyworthConfig.duibaHost}/sign/component/index?signOperatingId=${this.skyworthConfig.signOperatingId}&preview=false&_=${signInfoTimestamp}`;

            const signInfoHeaders = {
                'Accept': 'application/json, text/plain, */*',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty',
                'Referer': `https://${this.skyworthConfig.duibaHost}/sign/component/page?signOperatingId=${this.skyworthConfig.signOperatingId}&from=login&spm=74367.1.1.1`,
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Priority': 'u=1, i',
                'Cookie': cookies
            };

            const signInfoResult = await wxcode.task('get', getSignInfoUrl, signInfoHeaders);
            if (isDebug) console.log(`[DEBUG] 获取签到信息响应:`, signInfoResult ? 'success' : 'null');

            // 5. 调用获取token的API
            if (isDebug) console.log(`[DEBUG] 第四步：获取动态token`);

            const timestamp = Date.now();
            const getTokenUrl = `https://${this.skyworthConfig.duibaHost}/chw/ctoken/getToken`;
            const tokenData = `timestamp=${timestamp}`;

            // 构建与抓包数据完全一致的请求头
            const tokenHeaders = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185',
                'Accept': '*/*',
                'Origin': `https://${this.skyworthConfig.duibaHost}`,
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty',
                'Referer': `https://${this.skyworthConfig.duibaHost}/sign/component/page?signOperatingId=${this.skyworthConfig.signOperatingId}&from=login&spm=74367.1.1.1`,
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Priority': 'u=1, i'
            };

            // 添加cookie到请求头（这是关键）
            if (cookies) {
                tokenHeaders['Cookie'] = cookies;
                if (isDebug) console.log(`[DEBUG] 使用cookies长度: ${cookies.length}`);
            } else {
                console.log(`❌ 缺少必要的cookies，无法获取token`);
                return null;
            }

            if (isDebug) {
                console.log(`[DEBUG] 请求获取token: ${getTokenUrl}`);
                console.log(`[DEBUG] 请求数据: ${tokenData}`);
                console.log(`[DEBUG] Content-Length: ${tokenData.length}`);
            }

            const tokenResult = await wxcode.task('post', getTokenUrl, tokenHeaders, tokenData);

            if (isDebug) {
                console.log(`[DEBUG] Token响应类型:`, typeof tokenResult);
                if (tokenResult) {
                    console.log(`[DEBUG] Token响应长度:`, tokenResult.length || 'N/A');
                    if (typeof tokenResult === 'string' && tokenResult.length < 500) {
                        console.log(`[DEBUG] Token响应内容:`, tokenResult);
                    } else if (typeof tokenResult === 'object') {
                        console.log(`[DEBUG] Token响应对象:`, JSON.stringify(tokenResult, null, 2));
                    }
                } else {
                    console.log(`[DEBUG] Token响应为null或undefined`);
                }
            }

            // 检查响应格式，必须严格按照抓包数据的格式
            let tokenScript = null;
            let duibaToken = null;

            // 基于抓包数据，正确的响应应该是：{"success":true,"token":"..."}
            if (tokenResult && typeof tokenResult === 'object' && tokenResult.success === true && tokenResult.token) {
                tokenScript = tokenResult.token;
                if (isDebug) console.log(`[DEBUG] 从JSON响应中提取token脚本，长度: ${tokenScript.length}`);
            } else if (tokenResult && typeof tokenResult === 'string') {
                // 尝试解析字符串响应
                try {
                    const parsed = JSON.parse(tokenResult);
                    if (parsed.success === true && parsed.token) {
                        tokenScript = parsed.token;
                        if (isDebug) console.log(`[DEBUG] 从字符串响应中解析token脚本，长度: ${tokenScript.length}`);
                    }
                } catch (e) {
                    if (isDebug) console.log(`[DEBUG] 响应不是有效JSON: ${e.message}`);
                }
            }

            if (!tokenScript) {
                console.log(`❌ 无法从响应中提取token脚本`);
                if (isDebug) {
                    console.log(`[DEBUG] 期望的响应格式: {"success":true,"token":"..."}`);
                    console.log(`[DEBUG] 实际响应:`, tokenResult);
                }
                return null;
            }

            // 解析token脚本
            if (tokenScript && tokenScript.length > 0) {
                if (isDebug) console.log(`[DEBUG] 开始解析token脚本，长度: ${tokenScript.length}`);

                try {
                    // 基于抓包数据分析，token脚本是一个复杂的JavaScript代码
                    // 需要创建完整的执行环境
                    const vm = require('vm');

                    // 创建与浏览器环境相似的上下文
                    const context = {
                        // 基本JavaScript对象
                        String: String,
                        Math: Math,
                        Buffer: Buffer,
                        parseInt: parseInt,
                        parseFloat: parseFloat,
                        isNaN: isNaN,
                        isFinite: isFinite,
                        decodeURIComponent: decodeURIComponent,
                        encodeURIComponent: encodeURIComponent,

                        // 浏览器环境模拟
                        window: {},
                        document: {},
                        console: { log: () => {} },

                        // 全局变量
                        global: {},

                        // eval函数
                        eval: function(code) {
                            return vm.runInContext(code, context);
                        }
                    };

                    // 执行token脚本
                    vm.createContext(context);
                    const result = vm.runInContext(tokenScript, context, {
                        timeout: 20000,
                        displayErrors: true
                    });

                    if (isDebug) console.log(`[DEBUG] Token脚本执行结果类型:`, typeof result);
                    if (isDebug) console.log(`[DEBUG] Token脚本执行结果:`, result);

                    // 检查执行结果
                    if (typeof result === 'string' && result.length > 0) {
                        duibaToken = result;
                        if (isDebug) console.log(`[DEBUG] 从脚本执行结果中获取token: ${duibaToken}`);
                    } else {
                        if (isDebug) console.log(`[DEBUG] 脚本执行结果不是有效的token字符串`);
                    }
                } catch (vmError) {
                    console.log(`❌ 执行token脚本失败: ${vmError.message}`);
                    if (isDebug) {
                        console.log(`[DEBUG] 错误详情:`, vmError);
                        console.log(`[DEBUG] 脚本内容预览:`, tokenScript.substring(0, 300) + '...');
                    }
                }
            }

            // 如果执行JavaScript后仍然没有获取到token，则失败
            if (!duibaToken) {
                console.log(`❌ 无法从token脚本中提取有效token`);
                if (isDebug) {
                    console.log(`[DEBUG] Token脚本内容预览:`, tokenScript.substring(0, 200) + '...');
                }
                return null;
            }

            // 成功获取token
            this.credentials.duibaToken = duibaToken;
            this.credentials.duibaCookies = cookies;
            if (isDebug) console.log(`[DEBUG] 获取兑吧签到token成功: ${duibaToken}`);
            return duibaToken;
        } catch (error) {
            console.log(`获取兑吧签到token异常: ${error.message}`);
            if (isDebug) {
                console.error(error);
            }

            // 异常情况下也使用备用token
            const backupToken = 'zmqg40uw8';
            this.credentials.duibaToken = backupToken;
            if (isDebug) console.log(`[DEBUG] 异常情况使用备用token: ${backupToken}`);
            return backupToken;
        }
    }

    // 执行签到
    async executeSignin() {
        if (isDebug) console.log(`[DEBUG] 执行签到...`);

        const url = `https://${this.skyworthConfig.duibaHost}/sign/component/doSign`;
        const timestamp = Date.now();

        const params = new URLSearchParams({
            '_': timestamp.toString()
        });

        const data = new URLSearchParams({
            'signOperatingId': this.skyworthConfig.signOperatingId.toString(),
            'token': this.credentials.duibaToken
        });

        const headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json, text/plain, */*',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185',
            'Origin': `https://${this.skyworthConfig.duibaHost}`,
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': `https://${this.skyworthConfig.duibaHost}/sign/component/page?signOperatingId=${this.skyworthConfig.signOperatingId}&from=login&spm=74367.1.1.1`,
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Priority': 'u=1, i'
        };

        // 如果有保存的cookie，添加到请求头
        if (this.credentials.duibaCookies) {
            headers['Cookie'] = this.credentials.duibaCookies;
            if (isDebug) console.log(`[DEBUG] 使用保存的cookies进行签到`);
        }

        if (isDebug) {
            console.log(`[DEBUG] 签到请求URL: ${url}?${params}`);
            console.log(`[DEBUG] 签到请求数据: ${data.toString()}`);
            console.log(`[DEBUG] 使用token: ${this.credentials.duibaToken}`);
        }

        try {
            if (isDebug) {
                console.log(`[DEBUG] 准备发送签到请求...`);
                console.log(`[DEBUG] 请求URL: ${url}?${params}`);
                console.log(`[DEBUG] 请求头:`, JSON.stringify(headers, null, 2));
                console.log(`[DEBUG] 请求体: ${data.toString()}`);
            }

            const result = await wxcode.task('post', `${url}?${params}`, headers, data.toString());

            if (isDebug) {
                console.log(`[DEBUG] 签到响应类型:`, typeof result);
                console.log(`[DEBUG] 签到响应是否为null:`, result === null);
                console.log(`[DEBUG] 签到响应是否为undefined:`, result === undefined);
                console.log(`[DEBUG] 签到响应长度:`, result ? result.length : 'N/A');
                console.log(`[DEBUG] 签到响应内容:`, result ? JSON.stringify(result, null, 2) : 'null');
            }

            // 检查响应是否为空或无效
            if (!result) {
                console.log(`❌ 签到失败: 服务器无响应`);
                return false;
            }

            // 处理不同类型的响应
            let parsedResult = result;

            // 如果响应是字符串，尝试解析为JSON
            if (typeof result === 'string') {
                try {
                    parsedResult = JSON.parse(result);
                } catch (parseError) {
                    if (isDebug) console.log(`[DEBUG] 响应不是有效JSON，原始内容: ${result}`);
                    console.log(`❌ 签到失败: 响应格式错误`);
                    return false;
                }
            }

            // 检查不同的成功响应格式（基于抓包数据）
            if (parsedResult && parsedResult.success === true) {
                const signData = parsedResult.data || {};
                const orderNum = signData.orderNum;
                const signResult = signData.signResult;

                if (orderNum) {
                    console.log(`✅ 签到成功! 订单号: ${orderNum}, 签到结果: ${signResult}`);
                    return true;
                } else {
                    console.log(`✅ 签到成功! 结果: ${JSON.stringify(signData)}`);
                    return true;
                }
            } else if (parsedResult && parsedResult.data && parsedResult.data.signResult) {
                // 有些情况下成功响应的格式可能不同
                const signData = parsedResult.data;
                const orderNum = signData.orderNum;
                console.log(`✅ 签到成功! 订单号: ${orderNum}, 结果: ${JSON.stringify(signData)}`);
                return true;
            } else if (parsedResult && parsedResult.code === null && parsedResult.data) {
                // 基于抓包数据的另一种成功格式
                const signData = parsedResult.data;
                const orderNum = signData.orderNum;
                const signResult = signData.signResult;

                if (signResult === 100 || orderNum) {
                    console.log(`✅ 签到成功! 订单号: ${orderNum}, 签到结果: ${signResult}`);
                    return true;
                }
            } else {
                // 检查是否包含错误信息
                const errorMsg = parsedResult.errorMsg || parsedResult.desc || parsedResult.message || '未知错误';
                console.log(`❌ 签到失败: ${errorMsg}`);
                if (isDebug) {
                    console.log(`[DEBUG] 完整响应:`, JSON.stringify(parsedResult, null, 2));
                }
                return false;
            }
        } catch (error) {
            console.log(`❌ 签到异常: ${error.message}`);
            if (isDebug) {
                console.error(error);
            }
            return false;
        }
    }

    // 执行完整的创维小程序登录和签到流程
    async performSkyworthSignin() {
        try {
            if (isDebug) console.log(`[DEBUG] 开始创维小程序登录和签到流程...`);

            // 1. 确保有微信授权码
            if (!this.wxCode) {
                console.log(`❌ 缺少微信授权码，无法进行创维登录`);
                return false;
            }

            // 2. 交换ticket
            const ticket = await this.exchangeTicketForToken(this.wxCode);
            if (!ticket) {
                console.log(`❌ 获取ticket失败`);
                return false;
            }

            // 3. 注册/登录获取JWT Token
            const jwtToken = await this.registerOrLogin();
            if (!jwtToken) {
                console.log(`❌ 获取JWT Token失败`);
                return false;
            }

            // 4. 获取签到URL
            const signinUrl = await this.getSigninUrl();
            if (!signinUrl) {
                console.log(`❌ 获取签到URL失败`);
                return false;
            }

            // 5. 获取兑吧签到token
            const duibaToken = await this.getDuibaSigninToken(signinUrl);
            if (!duibaToken) {
                console.log(`❌ 获取兑吧签到token失败`);
                return false;
            }

            // 6. 执行签到
            const signinSuccess = await this.executeSignin();
            if (signinSuccess) {
                console.log(`🎉 创维小程序签到流程执行成功!`);
                return true;
            } else {
                console.log(`❌ 签到执行失败`);
                return false;
            }

        } catch (error) {
            console.log(`❌ 创维小程序签到流程异常: ${error.message}`);
            if (isDebug) {
                console.error(error);
            }
            return false;
        }
    }

    // 验证缓存数据是否仍然有效
    async validateCache() {
        if (!this.isLogin || !this.wxCode) return false;

        if (isDebug) console.log(`[DEBUG] 验证缓存数据有效性...`);

        try {
            // 尝试获取一个简单的信息来验证登录状态
            const testResult = await wxcode.getOpenid(this.wxid, this.appid);
            if (testResult.success) {
                if (isDebug) console.log(`[DEBUG] 缓存数据验证通过`);
                return true;
            }
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 缓存数据验证失败: ${error.message}`);
        }

        if (isDebug) console.log(`[DEBUG] 缓存数据已失效`);
        this.isLogin = false;
        return false;
    }

    // 执行完整的数据获取流程
    async performFullLogin() {
        if (isDebug) console.log(`[DEBUG] 执行完整的数据获取流程...`);

        // 1. 获取授权码并登录
        const loginSuccess = await this.getWxCodeAndLogin();
        if (!loginSuccess) {
            console.log(`[${this.wxid}] 获取授权码失败，跳过`);
            return false;
        }

        // 2. 获取必要的用户数据
        await this.getUserOpenid();     // 获取openid
        await this.getMobileInfo();     // 获取手机号
        await this.getUserProfile();    // 获取云函数用户个人信息

        // 3. 保存到缓存
        this.saveTokenCache();

        return true;
    }

    // 主要业务逻辑
    async run() {
        try {
            // 1. 尝试从缓存加载数据
            const cacheLoaded = this.loadTokenCache();

            if (cacheLoaded) {
                console.log(`📦 使用缓存的数据`);

                // 验证缓存数据是否仍然有效
                const cacheValid = await this.validateCache();
                if (!cacheValid) {
                    console.log(`⚠️ 缓存的数据已失效，重新获取...`);
                    const fullLoginSuccess = await this.performFullLogin();
                    if (!fullLoginSuccess) {
                        console.log(`[${this.wxid}] 完整登录失败，跳过`);
                        return;
                    }
                } else {
                    console.log(`✅ 缓存的数据有效`);
                }
            } else {
                // 2. 缓存无效或不存在，进行完整登录
                const fullLoginSuccess = await this.performFullLogin();
                if (!fullLoginSuccess) {
                    console.log(`[${this.wxid}] 完整登录失败，跳过`);
                    return;
                }
            }

            // 3. 执行创维小程序签到
            console.log(`🎯 开始执行创维小程序签到...`);
            const signinResult = await this.performSkyworthSignin();

            if (signinResult) {
                print(`✅ [${this.wxid}] 创维小程序签到成功`, true);
            } else {
                print(`❌ [${this.wxid}] 创维小程序签到失败`, true);
            }
        } catch (error) {
            console.log(`[${this.wxid}] 脚本执行出错：${error.message}`);
            if (isDebug) {
                console.error(error);
            }
        }
    }
}

// 主函数
async function main() {
    console.log(`🔔 脚本开始执行`);
    
    if (isDebug) {
        console.log(`[DEBUG] 调试模式已开启`);
        console.log(`[DEBUG] APPID: ${APPID}`);
    }
    
    if (!wxidList) {
        console.log(`❌ 未设置环境变量 TXX_WXID 或命令行参数 --wxid`);
        return;
    }

    // 处理单个wxid或多个wxid
    const wxids = cmdWxid ? [cmdWxid] : parseWxidList(wxidList);

    if (wxids.length === 0) {
        console.log(`❌ 没有找到有效的wxid`);
        return;
    }

    console.log(`📋 共找到 ${wxids.length} 个有效账号`);

    if (isDebug) {
        console.log(`[DEBUG] 账号列表: ${wxids.join(', ')}`);
    }

    // 逐个处理账号
    for (let i = 0; i < wxids.length; i++) {
        const wxid = wxids[i];
        console.log(`\n🚀 [${i + 1}/${wxids.length}] 开始处理账号: ${wxid}`);

        try {
            const script = new ScriptTemplate(wxid);
            await script.run();
            console.log(`✅ [${i + 1}/${wxids.length}] 账号 ${wxid} 处理完成`);
        } catch (error) {
            console.log(`❌ [${i + 1}/${wxids.length}] 账号 ${wxid} 处理失败: ${error.message}`);
            if (isDebug) {
                console.error(error);
            }
        }

        console.log('─'.repeat(60));

        // 如果不是最后一个账号，稍微延迟一下
        if (i < wxids.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }

    console.log(`\n🎉 所有账号处理完成！`);

    // 发送通知
    if (NOTICE_SWITCH && notice) {
        await sendMsg(notice);
    }
}

// 通知相关变量和函数
let notice = '';

function print(msg, is_notice = false) {
    let str = `${msg}`;
    console.log(str);
    if (NOTICE_SWITCH && is_notice) {
        notice += `${str}\n`;
    }
}

async function sendMsg(message) {
    try {
        let notify = '';
        try {
            notify = require('./sendNotify');
        } catch (e) {
            try {
                notify = require("../sendNotify");
            } catch (e2) {
                console.log('❌ 未找到sendNotify模块，无法发送通知');
                return;
            }
        }
        await notify.sendNotify(scriptName, message);
        console.log('📢 通知发送成功');
    } catch (error) {
        console.log(`❌ 通知发送失败: ${error.message}`);
    }
}

// 执行脚本
main().catch(console.error);
